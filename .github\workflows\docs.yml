name: Deploy Documentation

on:
  push:
    branches: [main, master]
    paths:
      - "docs/**"
      - "mkdocs.yml"
      - ".github/workflows/docs.yml"
  pull_request:
    branches: [main, master]
    paths:
      - "docs/**"
      - "mkdocs.yml"
      - ".github/workflows/docs.yml"

permissions:
  contents: read
  pages: write
  id-token: write
  pull-requests: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    outputs:
      preview_url: ${{ steps.preview.outputs.url }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install mkdocs mkdocs-material mkdocs-minify-plugin pymdown-extensions

      - name: Build documentation
        run: mkdocs build

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./site

      - name: Generate preview URL
        id: preview
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "url=https://${{ github.repository_owner }}.github.io/mcp-server-fuzzer/" >> $GITHUB_OUTPUT
          else
            echo "url=https://${{ github.repository_owner }}.github.io/mcp-server-fuzzer/" >> $GITHUB_OUTPUT
          fi

  # PR Comment job
  comment:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'
    steps:
      - name: Comment PR
        uses: actions/github-script@v7
        with:
          script: |
            const comment = `## Documentation Preview

            Your documentation changes have been built successfully!

            **Preview URL**: ${{ needs.build.outputs.preview_url }}

            The documentation will be automatically deployed to the main site when this PR is merged.

            ---
            *This comment was automatically generated by the documentation workflow.*`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Deployment job
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
