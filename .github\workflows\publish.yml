name: Publish on Tag

on:
  push:
    tags:
      - "v*"

permissions:
  contents: write

jobs:
  publish:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"

      - name: Install build & upload tools
        run: |
          python -m pip install --upgrade pip
          pip install "setuptools<61" wheel build twine

      - name: Build distributions
        run: |
          rm -rf build/ dist/ *.egg-info
          python -m build

      - name: Create GitHub Release if not exists
        run: |
          VERSION="${GITHUB_REF##*/}"
          gh release view "$VERSION" || gh release create "$VERSION" --title "$VERSION" --notes "Automated release"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload distributions to GitHub Release (clobber)
        run: |
          VERSION="${GITHUB_REF##*/}"
          gh release upload "$VERSION" dist/* --clobber
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish to PyPI
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          twine upload dist/*
