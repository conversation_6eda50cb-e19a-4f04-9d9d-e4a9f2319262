[tox]
envlist = ruff, tests
isolated_build = True

[testenv:ruff]
deps = ruff
commands = ruff check mcp_fuzzer tests
basepython = python3

[testenv:tests]
description = Run unit tests with coverage
deps =
    pytest
    pytest-cov
    pytest-asyncio
    httpx
    hypothesis
    rich
commands =
    pytest {posargs:-vv --cov=mcp_fuzzer --cov-report=term-missing}
basepython = python3

[pytest]
asyncio_mode = auto
