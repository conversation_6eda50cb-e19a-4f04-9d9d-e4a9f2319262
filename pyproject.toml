[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
exclude = ["servers*", "examples*"]

[project]
name = "mcp-fuzzer"
version = "0.1.9"
description = "MCP server fuzzer client and utilities"
readme = "README.md"
license = {file = "LICENSE"}
requires-python = ">=3.9"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
keywords = ["mcp", "fuzzing", "testing", "json-rpc"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: 3.14",
]
dependencies = [
    "httpx",
    "hypothesis",
    "rich",
    "pyyaml>=6.0",
    "psutil>=5.9.0",
    "emoji",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "ruff>=0.1.0",
    "black>=23.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.5.0",
    "tox>=4.0.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocs-minify-plugin>=0.7.0"
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0"
]

[project.scripts]
mcp-fuzzer = "mcp_fuzzer.__main__:run"

[project.urls]
Homepage = "https://github.com/agent-hellboy/mcp-server-fuzzer"
Repository = "https://github.com/agent-hellboy/mcp-server-fuzzer"
Issues = "https://github.com/agent-hellboy/mcp-server-fuzzer/issues"
Documentation = "https://agent-hellboy.github.io/mcp-server-fuzzer"

[tool.ruff]
target-version = "py39"

[tool.ruff.lint]
select = ["F", "E"]
per-file-ignores = { "tests/**" = ["F401", "F841"] }

[tool.pytest.ini_options]
addopts = "-q --maxfail=1 --disable-warnings --cov=mcp_fuzzer --cov-report=term-missing --cov-report=xml"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "auth: tests for the auth component",
    "cli: tests for the cli component",
    "client: tests for the client component",
    "config: tests for the config component",
    "fuzz_engine: tests for the fuzz_engine component",
    "fuzzer: tests for the fuzzer subcomponent",
    "runtime: tests for the runtime subcomponent",
    "strategy: tests for the strategy subcomponent",
    "safety_system: tests for the safety_system component",
    "transport: tests for the transport component",
    "integration: integration tests",
    "unit: unit tests"
]

[tool.coverage.run]
branch = true
source = ["mcp_fuzzer"]

[tool.coverage.report]
show_missing = true
skip_covered = false
