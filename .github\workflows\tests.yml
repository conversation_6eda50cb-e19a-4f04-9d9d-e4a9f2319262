name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      reason:
        description: "Why are you running the test workflow?"
        required: false
        default: "manual run"
      pytest_args:
        description: "Optional pytest args (e.g., -m 'unit and fuzz_engine')"
        required: false
        default: ""

permissions:
  contents: read
  id-token: write

concurrency:
  group: tests-${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pip'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install .
          pip install pytest pytest-cov pytest-asyncio
      - name: Run tests with coverage
        run: |
          pytest -vv --cov=mcp_fuzzer --cov-report=xml ${{ github.event.inputs.pytest_args }}
      - name: Check Codecov token
        id: codecov_token
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        run: |
          if [ -n "$CODECOV_TOKEN" ]; then
            echo "has_token=true" >> $GITHUB_OUTPUT
          else
            echo "has_token=false" >> $GITHUB_OUTPUT
          fi
      - name: Upload coverage to Codecov
        if: ${{ steps.codecov_token.outputs.has_token == 'true' && hashFiles('coverage.xml') != '' && (github.event_name != 'pull_request' || github.event.pull_request.head.repo.fork == false) }}
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage.xml
          fail_ci_if_error: true
